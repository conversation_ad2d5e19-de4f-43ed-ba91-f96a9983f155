package top.yogiczy.mytv.tv.ui.screensold.main.components

import androidx.compose.runtime.Composable
import androidx.compose.runtime.Stable
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableIntStateOf
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.rememberCoroutineScope
import androidx.compose.runtime.rememberUpdatedState
import androidx.compose.runtime.setValue
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Job
import kotlinx.coroutines.delay
import kotlinx.coroutines.launch
import top.yogiczy.mytv.core.data.entities.channel.Channel
import top.yogiczy.mytv.core.data.entities.channel.ChannelGroupList
import top.yogiczy.mytv.core.data.entities.channel.ChannelGroupList.Companion.channelFirstOrNull
import top.yogiczy.mytv.core.data.entities.channel.ChannelGroupList.Companion.channelGroupIdx
import top.yogiczy.mytv.core.data.entities.channel.ChannelGroupList.Companion.channelIdx
import top.yogiczy.mytv.core.data.entities.channel.ChannelGroupList.Companion.channelLastOrNull
import top.yogiczy.mytv.core.data.entities.channel.ChannelGroupList.Companion.channelList
import top.yogiczy.mytv.core.data.entities.channel.ChannelLine
import top.yogiczy.mytv.core.data.entities.channel.ChannelLineList
import top.yogiczy.mytv.core.data.entities.channel.ChannelList
import top.yogiczy.mytv.core.data.entities.iptvsource.IptvSource
import top.yogiczy.mytv.core.data.entities.iptvsource.IptvSourceList
import top.yogiczy.mytv.core.data.entities.epg.EpgProgramme
import top.yogiczy.mytv.core.data.entities.epg.EpgProgrammeReserve
import top.yogiczy.mytv.core.data.entities.epg.EpgProgrammeReserveList
import top.yogiczy.mytv.core.data.utils.ChannelUtil
import top.yogiczy.mytv.core.data.utils.Constants
import top.yogiczy.mytv.core.data.utils.Loggable
import top.yogiczy.mytv.core.util.utils.urlHost
import top.yogiczy.mytv.tv.ui.utils.Configs
import top.yogiczy.mytv.tv.ui.material.Snackbar
import top.yogiczy.mytv.tv.ui.screen.settings.SettingsViewModel
import top.yogiczy.mytv.tv.ui.screen.settings.settingsVM
import top.yogiczy.mytv.tv.ui.screensold.videoplayer.VideoPlayerState
import top.yogiczy.mytv.tv.ui.screensold.videoplayer.VideoPlayerPlaybackMode
import top.yogiczy.mytv.tv.ui.screensold.videoplayer.player.VideoPlayer
import top.yogiczy.mytv.tv.ui.screensold.videoplayer.player.Media3VideoPlayer
import top.yogiczy.mytv.tv.ui.screensold.videoplayer.rememberVideoPlayerState
import java.net.URI
import java.text.SimpleDateFormat
import java.util.Locale
import kotlin.math.max
import kotlin.math.min

@Stable
class MainContentState(
    private val coroutineScope: CoroutineScope,
    private val videoPlayerState: VideoPlayerState,
    private val channelGroupListProvider: () -> ChannelGroupList = { ChannelGroupList() },
    private val favoriteChannelListProvider: () -> ChannelList = { ChannelList() },
    private val settingsViewModel: SettingsViewModel,
) : Loggable("MainContentState") {
    private var _currentChannel by mutableStateOf(Channel())
    val currentChannel get() = _currentChannel

    private var _currentChannelLineIdx by mutableIntStateOf(0)
    val currentChannelLineIdx get() = _currentChannelLineIdx

    val currentChannelLine get() = _currentChannel.lineList[_currentChannelLineIdx]
    private var _currentPlaybackEpgProgramme by mutableStateOf<EpgProgramme?>(null)
    val currentPlaybackEpgProgramme get() = _currentPlaybackEpgProgramme

    // 保存用户点击的原始节目信息，用于进度条计算
    private var _originalPlaybackEpgProgramme by mutableStateOf<EpgProgramme?>(null)
    val originalPlaybackEpgProgramme get() = _originalPlaybackEpgProgramme

    private var _tempChannelScreenHideJob: Job? = null

    private var _isTempChannelScreenVisible by mutableStateOf(false)
    var isTempChannelScreenVisible
        get() = _isTempChannelScreenVisible
        set(value) {
            _isTempChannelScreenVisible = value
        }

    private var _isChannelScreenVisible by mutableStateOf(false)
    var isChannelScreenVisible
        get() = _isChannelScreenVisible
        set(value) {
            _isChannelScreenVisible = value
        }

    private var _isVideoPlayerControllerScreenVisible by mutableStateOf(false)
    var isVideoPlayerControllerScreenVisible
        get() = _isVideoPlayerControllerScreenVisible
        set(value) {
            _isVideoPlayerControllerScreenVisible = value
            // 当打开播放控制器且为直播模式时，设置虚拟48小时节目单
            if (value && _originalPlaybackEpgProgramme == null) {
                val nowAbs = System.currentTimeMillis()
                val virtualStart = nowAbs - (48 * 60 * 60 * 1000L) // 48小时前
                _originalPlaybackEpgProgramme = EpgProgramme(
                    startAt = virtualStart,
                    endAt = nowAbs,
                    title = "直播回看",
                )
                log.d("直播模式：设置虚拟48小时节目单 ${virtualStart} - ${nowAbs}")
            } else if (!value && _originalPlaybackEpgProgramme?.title == "直播回看") {
                // 关闭控制器时，如果是虚拟节目单且没有实际回放，则清除
                if (_currentPlaybackEpgProgramme == null) {
                    _originalPlaybackEpgProgramme = null
                    log.d("直播模式：清除虚拟48小时节目单")
                }
            }
        }

    private var _isIptvSourceScreenVisible by mutableStateOf(false)
    var isIptvSourceScreenVisible
        get() = _isIptvSourceScreenVisible
        set(value) {
            _isIptvSourceScreenVisible = value
        }

    private var _isQuickOpScreenVisible by mutableStateOf(false)
    var isQuickOpScreenVisible
        get() = _isQuickOpScreenVisible
        set(value) {
            _isQuickOpScreenVisible = value
        }
    
    private var _isEpgScreenVisible by mutableStateOf(false)
    var isEpgScreenVisible
        get() = _isEpgScreenVisible
        set(value) {
            _isEpgScreenVisible = value
        }

    private var _isChannelLineScreenVisible by mutableStateOf(false)
    var isChannelLineScreenVisible
        get() = _isChannelLineScreenVisible
        set(value) {
            _isChannelLineScreenVisible = value
        }

    private var _isVideoPlayerDisplayModeScreenVisible by mutableStateOf(false)
    var isVideoPlayerDisplayModeScreenVisible
        get() = _isVideoPlayerDisplayModeScreenVisible
        set(value) {
            _isVideoPlayerDisplayModeScreenVisible = value
        }

    private var _isVideoTracksScreenVisible by mutableStateOf(false)
    var isVideoTracksScreenVisible
        get() = _isVideoTracksScreenVisible
        set(value) {
            _isVideoTracksScreenVisible = value
        }
    
    private var _triggerPlayerReinit by mutableStateOf(false)
    var triggerPlayerReinit
        get() = _triggerPlayerReinit
        set(value) {
            _triggerPlayerReinit = value
        }

    private fun updatePlayerTrigger(){
        _triggerPlayerReinit = !_triggerPlayerReinit
    } 

    private var _isAudioTracksScreenVisible by mutableStateOf(false)
    var isAudioTracksScreenVisible
        get() = _isAudioTracksScreenVisible
        set(value) {
            _isAudioTracksScreenVisible = value
        }

    private var _isSubtitleTracksScreenVisible by mutableStateOf(false)
    var isSubtitleTracksScreenVisible
        get() = _isSubtitleTracksScreenVisible
        set(value) {
            _isSubtitleTracksScreenVisible = value
        }

    init {
        val channelGroupList = channelGroupListProvider()

        changeCurrentChannel(settingsViewModel.iptvChannelLastPlay.isEmptyOrElse {
            channelGroupList.channelFirstOrNull() ?: Channel.EMPTY
        })

        videoPlayerState.onReady {
            settingsViewModel.iptvChannelLinePlayableUrlList += currentChannelLine.url
            settingsViewModel.iptvChannelLinePlayableHostList += currentChannelLine.url.urlHost()

            // 如果是seekTo回放模式且有回放节目，执行seekTo跳转
            if (settingsViewModel.videoPlayerPlaybackMode == VideoPlayerPlaybackMode.SEEK_TO &&
                _currentPlaybackEpgProgramme != null) {
                val currentTime = System.currentTimeMillis()
                val seekPosition = currentTime - _currentPlaybackEpgProgramme!!.startAt
                if (seekPosition > 0) {
                    videoPlayerState.seekTo(seekPosition)
                    log.d("seekTo回放模式: 跳转到位置 ${seekPosition}ms")
                }
            }
        }

        videoPlayerState.onError {
            if (_currentPlaybackEpgProgramme != null) return@onError

            settingsViewModel.iptvChannelLinePlayableUrlList -= currentChannelLine.url
            settingsViewModel.iptvChannelLinePlayableHostList -= currentChannelLine.url.urlHost()

            if (_currentChannelLineIdx < _currentChannel.lineList.size - 1) {
                changeCurrentChannel(_currentChannel, _currentChannelLineIdx + 1)
            }
        }

        videoPlayerState.onInterrupt {
            changeCurrentChannel(
                _currentChannel,
                _currentChannelLineIdx,
                _currentPlaybackEpgProgramme
            )
        }

        videoPlayerState.onIsBuffering { isBuffering ->
            if (isBuffering) {
                _isTempChannelScreenVisible = true
            } else {
                _tempChannelScreenHideJob?.cancel()
                _tempChannelScreenHideJob = coroutineScope.launch {
                    val name = _currentChannel.name
                    val lineIdx = _currentChannelLineIdx
                    delay(Constants.UI_TEMP_CHANNEL_SCREEN_SHOW_DURATION)
                    if (name == _currentChannel.name && lineIdx == _currentChannelLineIdx) {
                        _isTempChannelScreenVisible = false
                    }
                }
            }
        }
    }

    private fun getPrevFavoriteChannel(): Channel? {
        if (!settingsViewModel.iptvChannelFavoriteListVisible) return null

        val channelGroupList = channelGroupListProvider()
        val favoriteChannelList = favoriteChannelListProvider()

        if (_currentChannel !in favoriteChannelList) return null

        val currentIdx = favoriteChannelList.indexOf(_currentChannel)

        return favoriteChannelList.getOrElse(currentIdx - 1) {
            if (settingsViewModel.iptvChannelChangeListLoop) favoriteChannelList.lastOrNull()
            else channelGroupList.channelLastOrNull()
        }
    }

    private fun getNextFavoriteChannel(): Channel? {
        if (!settingsViewModel.iptvChannelFavoriteListVisible) return null

        val channelGroupList = channelGroupListProvider()
        val favoriteChannelList = favoriteChannelListProvider()

        if (_currentChannel !in favoriteChannelList) return null

        val currentIdx = favoriteChannelList.indexOf(_currentChannel)

        return favoriteChannelList.getOrElse(currentIdx + 1) {
            if (settingsViewModel.iptvChannelChangeListLoop) favoriteChannelList.firstOrNull()
            else channelGroupList.channelFirstOrNull()
        }
    }

    private fun getPrevChannel(): Channel {
        return getPrevFavoriteChannel() ?: run {
            val channelGroupList = channelGroupListProvider()
            
            if (settingsViewModel.iptvChannelChangeCrossGroup) {
                // 跨分组切换逻辑
                val currentIdx = channelGroupList.channelIdx(_currentChannel)
                val prevIdx = if (currentIdx <= 0) {
                    if (settingsViewModel.iptvChannelChangeListLoop) 
                        channelGroupList.channelList.size - 1 
                    else 
                        0
                } else {
                    currentIdx - 1
                }
                channelGroupList.channelList.getOrElse(prevIdx) {
                    channelGroupList.channelLastOrNull() ?: Channel()
                }
            } else {
                // 分组内切换逻辑
                val group = channelGroupList.getOrElse(channelGroupList.channelGroupIdx(_currentChannel)) { channelGroupList.first() }
                val currentIdx = group.channelList.indexOf(_currentChannel)
                if (currentIdx <= 0) {
                    // 当前是分组内第一个
                    if (settingsViewModel.iptvChannelChangeListLoop) {
                        // 循环开启时，返回分组内最后一个
                        group.channelList.last()
                    } else {
                        // 循环关闭时，保持当前频道不变
                        _currentChannel
                    }
                } else {
                    // 返回分组内前一个
                    group.channelList[currentIdx - 1]
                }
            }
        }
    }

    private fun getNextChannel(): Channel {
        return getNextFavoriteChannel() ?: run {
            val channelGroupList = channelGroupListProvider()
            
            if (settingsViewModel.iptvChannelChangeCrossGroup) {
                // 跨分组切换逻辑
                val currentIdx = channelGroupList.channelIdx(_currentChannel)
                val nextIdx = if (currentIdx >= channelGroupList.channelList.size - 1) {
                    if (settingsViewModel.iptvChannelChangeListLoop) 
                        0 
                    else 
                        channelGroupList.channelList.size - 1
                } else {
                    currentIdx + 1
                }
                channelGroupList.channelList.getOrElse(nextIdx) {
                    channelGroupList.channelFirstOrNull() ?: Channel()
                }
            } else {
                // 分组内切换逻辑
                val group = channelGroupList.getOrElse(channelGroupList.channelGroupIdx(_currentChannel)) { channelGroupList.first() }
                val currentIdx = group.channelList.indexOf(_currentChannel)
                if (currentIdx >= group.channelList.size - 1) {
                    // 当前是分组内最后一个
                    if (settingsViewModel.iptvChannelChangeListLoop) {
                        // 循环开启时，返回分组内第一个
                        group.channelList.first()
                    } else {
                        // 循环关闭时，保持当前频道不变
                        _currentChannel
                    }
                } else {
                    // 返回分组内下一个
                    group.channelList[currentIdx + 1]
                }
            }
        }
    }

    private fun getLineIdx(lineList: ChannelLineList, lineIdx: Int? = null): Int {
        val idx = if (lineIdx == null) {
            val idx = lineList.indexOfFirst { line ->
                settingsViewModel.iptvChannelLinePlayableUrlList.contains(line.url)
            }

            if (idx < 0) {
                lineList.indexOfFirst { line ->
                    settingsViewModel.iptvChannelLinePlayableHostList.contains(line.url.urlHost())
                }
            } else idx
        } else (lineIdx + lineList.size) % lineList.size

        return max(0, min(idx, lineList.size - 1))
    }

    fun changeCurrentChannel(
        channel: Channel,
        lineIdx: Int? = null, 
        playbackEpgProgramme: EpgProgramme? = null,
    ) {
        settingsViewModel.iptvChannelLastPlay = channel
        val realLineIdx = getLineIdx(channel.lineList, lineIdx)

        // if (channel == _currentChannel && realLineIdx == _currentChannelLineIdx && playbackEpgProgramme == _currentPlaybackEpgProgramme) return
        
        if (channel == _currentChannel && realLineIdx != _currentChannelLineIdx) {
            settingsViewModel.iptvChannelLinePlayableUrlList -= currentChannelLine.url
            settingsViewModel.iptvChannelLinePlayableHostList -= currentChannelLine.url.urlHost()
        }

        _isTempChannelScreenVisible = true

        val oldChannel = _currentChannel

        _currentChannel = channel
        _currentChannelLineIdx = realLineIdx
        
        // 处理原始节目信息的保存
        if (playbackEpgProgramme != null) {
            // 如果是切换到不同频道或从直播切换到回放，或者原始节目为空，保存新的原始节目
            if (_currentPlaybackEpgProgramme == null || channel != oldChannel || _originalPlaybackEpgProgramme == null) {
                _originalPlaybackEpgProgramme = playbackEpgProgramme
            }
            // 否则保持原有的原始节目信息不变（用于快进快退场景）
        } else {
            // 切换回直播时，清空原始节目信息
            _originalPlaybackEpgProgramme = null
        }
        
        _currentPlaybackEpgProgramme = playbackEpgProgramme
        currentChannelLine.playbackUrl = null
        var url = currentChannelLine.url

        // 根据回放方式设置处理回放
        if (_currentPlaybackEpgProgramme != null) {
            when (settingsViewModel.videoPlayerPlaybackMode) {
                VideoPlayerPlaybackMode.RELOAD_URL -> {
                    // 重载URL跳转方式（原有逻辑）
                    if(currentChannelLine.playbackType != null){
                        var playbackFormat = currentChannelLine.playbackFormat ?: ""
                        val timeFormat = SimpleDateFormat("yyyyMMddHHmmss", Locale.getDefault())
                        val timeFormatUTC = SimpleDateFormat("yyyyMMddHHmmss", Locale.getDefault())
                        timeFormatUTC.timeZone = java.util.TimeZone.getTimeZone("UTC")
                        val tfY = SimpleDateFormat("yyyy", Locale.getDefault())
                        val tfM = SimpleDateFormat("MM", Locale.getDefault())
                        val tfD = SimpleDateFormat("dd", Locale.getDefault())
                        val tfH = SimpleDateFormat("HH", Locale.getDefault())
                        val tfm = SimpleDateFormat("mm", Locale.getDefault())
                        val tfS = SimpleDateFormat("ss", Locale.getDefault())
                        val nowTime = System.currentTimeMillis()
                        playbackFormat.apply{
                                _currentPlaybackEpgProgramme?.let { epgProgramme ->
                                    replace("{utc}", timeFormatUTC.format(epgProgramme.startAt))
                                    replace("\${start}", timeFormat.format(epgProgramme.startAt))
                                    replace("{lutc}", timeFormatUTC.format(nowTime))
                                    replace("\${now}", tfY.format(nowTime))
                                    replace("\${timestamp}", timeFormat.format(nowTime))
                                    replace("{utcend}", timeFormatUTC.format(epgProgramme.endAt))
                                    replace("\${end}", timeFormat.format(epgProgramme.endAt))
                                    replace("{Y}", tfY.format(epgProgramme.startAt))
                                    replace("{m}", tfM.format(epgProgramme.startAt))
                                    replace("{d}", tfD.format(epgProgramme.startAt))
                                    replace("{H}", tfH.format(epgProgramme.startAt))
                                    replace("{M}", tfm.format(epgProgramme.startAt))
                                    replace("{S}", tfS.format(epgProgramme.startAt))
                                }
                        }
                        playbackFormat = ChannelUtil.replacePlaybackFormat(playbackFormat, _currentPlaybackEpgProgramme!!.startAt, nowTime, _currentPlaybackEpgProgramme!!.endAt)?:""
                        url = when (currentChannelLine.playbackType ?: 10) {
                            0 -> playbackFormat
                            1 -> "$url$playbackFormat"
                            // 2 ->
                            // 3 ->
                            // 4 ->
                            else -> url
                        }
                    }else{
                        val timeFormat = SimpleDateFormat("yyyyMMddHHmmss", Locale.getDefault())
                        val query = listOf(
                            "playseek=",
                            timeFormat.format(_currentPlaybackEpgProgramme!!.startAt),
                            "-",
                            timeFormat.format(_currentPlaybackEpgProgramme!!.endAt),
                        ).joinToString("")
                        url = if (URI(url).query.isNullOrBlank()) "$url?$query" else "$url&$query"
                        if (Configs.iptvPLTVToTVOD){
                            url = ChannelUtil.urlToCanPlayback(url)
                        }
                    }
                    currentChannelLine.playbackUrl = url
                    log.d("重载URL回放模式: $url")
                }
                VideoPlayerPlaybackMode.SEEK_TO -> {
                    // 播放器seekTo跳转方式
                    // 不修改URL，保持原始直播流地址
                    // 回放逻辑将在播放器准备完成后通过seekTo实现
                }
            }
        }
        
        val line = currentChannelLine.copy(url = url)

        log.d("播放${_currentChannel.name}（${_currentChannelLineIdx + 1}/${_currentChannel.lineList.size}）: $line")

        if (line.hybridType == ChannelLine.HybridType.WebView) {
            updatePlayerTrigger()
            log.i("检测到WebView类型URL: ${line.url}")
            log.i("正在使用WebView打开而不是视频播放器")
            videoPlayerState.metadata = VideoPlayer.Metadata()
            videoPlayerState.stop()
        } else {
            currentChannelLine.playbackUrl = null
            log.i("检测到普通视频URL: ${line.url}")
            log.i("hybridType: ${line.hybridType}, 使用视频播放器播放")
            if(line.url.startsWith("rtsp://") && line.url.contains("smil") && (videoPlayerState.instance is Media3VideoPlayer)){
                settingsViewModel.videoPlayerCore = Configs.VideoPlayerCore.IJK // Media3 1.6.0 不支持rtsp有效负载类型33
            }else{
                videoPlayerState.prepare(line)
            }
            
        }
    }

    fun changeCurrentChannelToPrev() {
        changeCurrentChannel(getPrevChannel())
    }

    fun changeCurrentChannelToNext() {
        changeCurrentChannel(getNextChannel())
    }

    fun reverseEpgProgrammeOrNot(channel: Channel, programme: EpgProgramme) {
        val reverse = settingsViewModel.epgChannelReserveList.firstOrNull {
            it.test(channel, programme)
        }

        if (reverse != null) {
            settingsViewModel.epgChannelReserveList =
                EpgProgrammeReserveList(settingsViewModel.epgChannelReserveList - reverse)
            Snackbar.show("取消预约：${reverse.channel} - ${reverse.programme}")
        } else {
            val newReserve = EpgProgrammeReserve(
                channel = channel.name,
                programme = programme.title,
                startAt = programme.startAt,
                endAt = programme.endAt,
            )

            settingsViewModel.epgChannelReserveList =
                EpgProgrammeReserveList(settingsViewModel.epgChannelReserveList + newReserve)
            Snackbar.show("已预约：${channel.name} - ${programme.title}")
        }
    }

    fun supportPlayback(
        channel: Channel = _currentChannel,
        lineIdx: Int? = _currentChannelLineIdx,
    ): Boolean {
        val currentLineIdx = getLineIdx(channel.lineList, lineIdx)
        return channel.lineList[currentLineIdx].playbackType != null || 
        ChannelUtil.urlSupportPlayback(channel.lineList[currentLineIdx].url)
    }
}

@Composable
fun rememberMainContentState(
    coroutineScope: CoroutineScope = rememberCoroutineScope(),
    videoPlayerState: VideoPlayerState = rememberVideoPlayerState(),
    channelGroupListProvider: () -> ChannelGroupList = { ChannelGroupList() },
    favoriteChannelListProvider: () -> ChannelList = { ChannelList() },
    settingsViewModel: SettingsViewModel = settingsVM,
): MainContentState {
    val channelGroupListProviderUpdated by rememberUpdatedState(channelGroupListProvider)
    val favoriteChannelListProviderUpdated by rememberUpdatedState(favoriteChannelListProvider)

    val channelGroupListSnapshot = channelGroupListProviderUpdated()

    return remember(
        settingsViewModel.videoPlayerCore,
        channelGroupListSnapshot
    ) {
        MainContentState(
            coroutineScope = coroutineScope,
            videoPlayerState = videoPlayerState,
            channelGroupListProvider = channelGroupListProvider,
            favoriteChannelListProvider = favoriteChannelListProviderUpdated,
            settingsViewModel = settingsViewModel,
        )
    }
}