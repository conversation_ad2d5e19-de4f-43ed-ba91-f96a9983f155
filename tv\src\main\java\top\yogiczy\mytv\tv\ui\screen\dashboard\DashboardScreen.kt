package top.yogiczy.mytv.tv.ui.screen.dashboard

import android.graphics.Bitmap
import androidx.compose.animation.AnimatedVisibility
import androidx.compose.animation.core.Animatable
import androidx.compose.animation.core.tween
import androidx.compose.animation.fadeIn
import androidx.compose.animation.fadeOut
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.background
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.Image
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.PaddingValues
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.foundation.lazy.rememberLazyListState
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.SyncAlt
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.setValue
import androidx.compose.runtime.snapshotFlow
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.alpha
import androidx.compose.ui.draw.blur
import androidx.compose.ui.draw.BlurredEdgeTreatment
import androidx.compose.ui.focus.onFocusChanged
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.ImageBitmap
import androidx.compose.ui.graphics.asImageBitmap
import androidx.compose.ui.graphics.painter.BitmapPainter
import androidx.compose.ui.layout.ContentScale
import androidx.compose.ui.graphics.RectangleShape
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import androidx.tv.material3.ClickableSurfaceDefaults
import androidx.tv.material3.Icon
import androidx.compose.material3.MaterialTheme
import androidx.tv.material3.Surface
import androidx.tv.material3.Text
import kotlinx.coroutines.launch
import top.yogiczy.mytv.core.data.entities.channel.Channel
import top.yogiczy.mytv.core.data.entities.channel.ChannelFavoriteList
import top.yogiczy.mytv.core.data.entities.channel.ChannelList
import top.yogiczy.mytv.core.data.entities.epg.EpgList
import top.yogiczy.mytv.core.data.entities.iptvsource.IptvSource
import top.yogiczy.mytv.tv.ui.rememberChildPadding
import top.yogiczy.mytv.tv.ui.screen.components.AppScreen
import top.yogiczy.mytv.tv.ui.screen.dashboard.components.DashboardImmersiveFavoriteList
import top.yogiczy.mytv.tv.ui.screen.dashboard.components.DashboardHistoryList
import top.yogiczy.mytv.tv.ui.screen.dashboard.components.DashboardModuleList
import top.yogiczy.mytv.tv.ui.theme.MyTvTheme
import top.yogiczy.mytv.tv.ui.utils.focusOnLaunched
import top.yogiczy.mytv.tv.ui.utils.handleKeyEvents
import top.yogiczy.mytv.tv.ui.utils.ifElse
import top.yogiczy.mytv.tv.ui.screen.settings.settingsVM

@Composable
fun DashboardScreen(
    modifier: Modifier = Modifier,
    currentIptvSourceProvider: () -> IptvSource = { IptvSource() },
    channelFavoriteListProvider: () -> ChannelFavoriteList = { ChannelFavoriteList() },
    channelHistoryListProvider: () -> ChannelList = { ChannelList() },
    onChannelSelected: (Channel) -> Unit = {},
    onChannelFavoriteToggle: (Channel) -> Unit = {},
    epgListProvider: () -> EpgList = { EpgList() },
    toLiveScreen: () -> Unit = {},
    toChannelsScreen: () -> Unit = {},
    toFavoritesScreen: () -> Unit = {},
    toSearchScreen: () -> Unit = {},
    toMultiViewScreen: () -> Unit = {},
    toPushScreen: () -> Unit = {},
    toSettingsScreen: () -> Unit = {},
    toAboutScreen: () -> Unit = {},
    toSettingsIptvSourceScreen: () -> Unit = {},
    onReload: () -> Unit = {},
    onBackPressed: () -> Unit = {},
) {
    val childPadding = rememberChildPadding()

    val favoriteEnable = settingsVM.iptvChannelFavoriteEnable
    val historyEnable = settingsVM.iptvChannelHistoryEnable
    var preview by remember { mutableStateOf<Bitmap?>(null) }

    val lazyListState = rememberLazyListState()
    var isModuleListVisible by remember { mutableStateOf(false) }
    LaunchedEffect(lazyListState) {
        snapshotFlow { 
            val layoutInfo = lazyListState.layoutInfo
            val visibleItems = layoutInfo.visibleItemsInfo
            val viewportEndOffset = layoutInfo.viewportEndOffset

            visibleItems.any { it.index == 1 && (it.offset + it.size / 2) <= viewportEndOffset }
        }.collect { visible ->
            isModuleListVisible = visible
        }
    }
    AppScreen(
        modifier = modifier,
        onBackPressed = onBackPressed,
    ) {
        Box(
            modifier = Modifier
                .fillMaxSize()
        ) {
            AnimatedVisibility(
                preview != null,
                enter = fadeIn(),
                exit = fadeOut(),
            ) {
                val emptyBitmap = ImageBitmap(1, 1)

                Image(
                    modifier = Modifier.fillMaxSize()
                    .ifElse(isModuleListVisible,
                        Modifier.blur(
                            radiusX = 50.dp,
                            radiusY = 50.dp,
                            edgeTreatment = BlurredEdgeTreatment.Rectangle
                        )
                    ),
                    painter = BitmapPainter(preview?.asImageBitmap() ?: emptyBitmap),
                    contentDescription = null,
                    contentScale = ContentScale.Crop,
                )
            }
            if(preview != null) {
                Box(
                    modifier = Modifier
                        .fillMaxSize()
                        // .ifElse(!isModuleListVisible,
                        //     Modifier.
                        .background(
                            brush = androidx.compose.ui.graphics.Brush.verticalGradient(
                                colors = listOf(
                                    MaterialTheme.colorScheme.surfaceContainer.copy(alpha = 0.7f),
                                    MaterialTheme.colorScheme.surfaceContainer.copy(alpha = 0.45f)
                                ),
                                startY = 0f,
                                endY = Float.POSITIVE_INFINITY
                            )
                        )
                )
            }
            LazyColumn(
                state = lazyListState,
                contentPadding = PaddingValues(bottom = childPadding.bottom),
                verticalArrangement = Arrangement.spacedBy(20.dp),
            ) {
                item {
                    DashboardImmersiveFavoriteList(
                        modifier = Modifier.padding(
                            start = childPadding.start,
                            top = 30.dp,
                            end = childPadding.end,
                        ),
                        channelFavoriteListProvider = channelFavoriteListProvider,
                        onChannelSelected = onChannelSelected,
                        onChannelUnFavorite = onChannelFavoriteToggle,
                        epgListProvider = epgListProvider,
                        currentIptvSourceProvider = currentIptvSourceProvider,
                        toSettingsIptvSourceScreen = toSettingsIptvSourceScreen,
                        onReload = onReload,
                        onPreviewChanged = { preview = it }
                    )
                }
                
                
                item {
                    DashboardModuleList(
                        // modifier = Modifier.focusOnLaunched(),
                        toLiveScreen = toLiveScreen,
                        toChannelsScreen = toChannelsScreen,
                        toFavoritesScreen = toFavoritesScreen,
                        toSearchScreen = toSearchScreen,
                        toMultiViewScreen = toMultiViewScreen,
                        toPushScreen = toPushScreen,
                        toSettingsScreen = toSettingsScreen,
                        toAboutScreen = toAboutScreen,
                    )
                }


                if (historyEnable) {
                    item {
                        DashboardHistoryList(
                            channelHistoryListProvider = channelHistoryListProvider,
                            onChannelSelected = onChannelSelected,
                            epgListProvider = epgListProvider,
                        )
                    }
                }
            }
        }
    }
}

@Composable
fun DashboardScreeIptvSource(
    modifier: Modifier = Modifier,
    currentIptvSourceProvider: () -> IptvSource = { IptvSource() },
    toSettingsIptvSourceScreen: () -> Unit = {},
    clearCurrentIptvSourceCache: () -> Unit = {},
) {
    val currentIptvSource = currentIptvSourceProvider()

    var isFocused by remember { mutableStateOf(false) }

    Surface(
        modifier = modifier
            .onFocusChanged { isFocused = it.isFocused || it.hasFocus }
            .handleKeyEvents(
                onSelect = toSettingsIptvSourceScreen,
                onLongSelect = clearCurrentIptvSourceCache,
            ),
        colors = ClickableSurfaceDefaults.colors(
            containerColor = MaterialTheme.colorScheme.surfaceContainer,
            contentColor = MaterialTheme.colorScheme.onSurface,
            focusedContainerColor = MaterialTheme.colorScheme.primary,
            focusedContentColor = MaterialTheme.colorScheme.onPrimary,
        ),
        scale = ClickableSurfaceDefaults.scale(focusedScale = 1.05f),
        shape = ClickableSurfaceDefaults.shape(RectangleShape),
        onClick = {},
    ) {
        Row(
            modifier = Modifier.padding(horizontal = 16.dp, vertical = 8.dp),
            verticalAlignment = Alignment.CenterVertically,
            horizontalArrangement = Arrangement.spacedBy(4.dp),
        ) {
            Text(currentIptvSource.name)
            if (isFocused) Icon(Icons.Default.SyncAlt, contentDescription = null)
        }
    }
}

@Preview(device = "id:Android TV (720p)")
@Composable
private fun DashboardScreenScreen() {
    MyTvTheme {
        DashboardScreen(
            currentIptvSourceProvider = { IptvSource(name = "默认订阅源1") },
            channelFavoriteListProvider = { ChannelFavoriteList.EXAMPLE },
            epgListProvider = { EpgList.example(ChannelList.EXAMPLE) },
        )
        // PreviewWithLayoutGrids { }
    }
}