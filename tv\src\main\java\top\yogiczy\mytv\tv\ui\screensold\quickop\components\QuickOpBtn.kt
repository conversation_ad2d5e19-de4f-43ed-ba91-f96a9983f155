package top.yogiczy.mytv.tv.ui.screensold.quickop.components

import androidx.compose.animation.animateContentSize
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.vector.ImageVector
import androidx.compose.ui.unit.dp
import androidx.tv.material3.ClickableSurfaceDefaults
import androidx.tv.material3.Icon
import androidx.compose.material3.ButtonGroupDefaults
import androidx.compose.material3.ExperimentalMaterial3ExpressiveApi
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.ShapeDefaults
import androidx.tv.material3.Surface
import androidx.tv.material3.Text
import top.yogiczy.mytv.tv.ui.utils.handleKeyEvents

@OptIn(ExperimentalMaterial3ExpressiveApi::class)
@Composable
fun QuickOpBtn(
    modifier: Modifier = Modifier,
    title: String,
    pos: Int,
    imageVector: ImageVector? = null,
    onSelect: () -> Unit = {},
    onLongSelect: () -> Unit = {},
) {
    Surface(
        modifier = modifier
            .handleKeyEvents(onSelect = onSelect, onLongSelect = onLongSelect),
        onClick = {},
        // shape = ClickableSurfaceDefaults.shape(shape = RoundedCornerShape(50)),
        shape = ClickableSurfaceDefaults.shape(
            shape = when(pos) {
                0 -> ButtonGroupDefaults.connectedLeadingButtonShape
                1 -> ShapeDefaults.Small
                else -> ButtonGroupDefaults.connectedTrailingButtonShape
            },
            focusedShape = ButtonGroupDefaults.connectedButtonCheckedShape,
            pressedShape = when(pos) {
                0 -> ButtonGroupDefaults.connectedLeadingButtonPressShape
                1 -> ButtonGroupDefaults.connectedMiddleButtonPressShape
                else -> ButtonGroupDefaults.connectedTrailingButtonPressShape
            },
        ),
        colors = ClickableSurfaceDefaults.colors(
            containerColor = MaterialTheme.colorScheme.surfaceContainer,
            contentColor = MaterialTheme.colorScheme.onSurface,
            focusedContainerColor = MaterialTheme.colorScheme.primary,
            focusedContentColor = MaterialTheme.colorScheme.onPrimary,
        ),
    ) {
        Row(
            modifier = Modifier.padding(horizontal = 16.dp, vertical = 8.dp),
            horizontalArrangement = Arrangement.spacedBy(8.dp),
            verticalAlignment = Alignment.CenterVertically,
        ) {
            imageVector?.let {
                Icon(it, null, Modifier.size(20.dp))
            }

            Text(text = title, modifier = Modifier.animateContentSize())
        }
    }
}
