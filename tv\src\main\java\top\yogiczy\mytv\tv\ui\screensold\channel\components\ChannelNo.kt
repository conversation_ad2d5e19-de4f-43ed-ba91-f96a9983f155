package top.yogiczy.mytv.tv.ui.screensold.channel.components

import androidx.compose.foundation.background
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.height
import androidx.compose.runtime.Composable
import androidx.compose.ui.Modifier
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import androidx.compose.material3.MaterialTheme
import androidx.tv.material3.Text
import top.yogiczy.mytv.tv.ui.theme.MyTvTheme

@Composable
fun ChannelNumber(
    modifier: Modifier = Modifier,
    channelNumberProvider: () -> String = { "" },
) {
    if(channelNumberProvider().isNotEmpty()) {
        Box(
            modifier = modifier
                .background(
                    color = MaterialTheme.colorScheme.tertiary.copy(alpha = 0.5f),
                    shape = MaterialTheme.shapes.medium
                )
                .height(48.dp)
                .padding(horizontal = 12.dp)
        ) {
            Text(
                text = channelNumberProvider(),
                style = MaterialTheme.typography.displayMedium,
                color = MaterialTheme.colorScheme.onTertiary,
            )
        }
    }
}

@Preview
@Composable
private fun ChannelNumberPreview() {
    MyTvTheme {
        ChannelNumber(channelNumberProvider = { "01" })
    }
}