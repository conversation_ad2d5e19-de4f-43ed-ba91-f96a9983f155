package top.yogiczy.mytv.tv.ui.screen.update

import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.width
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.rememberCoroutineScope
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import top.yogiczy.mytv.tv.ui.material.ListItem
import androidx.compose.material3.ListItemDefaults
import androidx.compose.material3.MaterialTheme
import androidx.tv.material3.Text
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.launch
import top.yogiczy.mytv.core.data.entities.git.GitRelease
import top.yogiczy.mytv.core.data.utils.Globals
import top.yogiczy.mytv.core.util.utils.ApkInstaller
import top.yogiczy.mytv.tv.ui.screen.components.AppScreen
import top.yogiczy.mytv.tv.ui.theme.MyTvTheme
import top.yogiczy.mytv.tv.ui.theme.SAFE_AREA_HORIZONTAL_PADDING
import top.yogiczy.mytv.tv.ui.tooling.PreviewWithLayoutGrids
import top.yogiczy.mytv.tv.ui.utils.focusOnLaunched
import top.yogiczy.mytv.tv.ui.utils.gridColumns
import top.yogiczy.mytv.tv.ui.utils.handleKeyEvents
import top.yogiczy.mytv.tv.ui.utils.rememberCanRequestPackageInstallsPermission
import java.io.File
import top.yogiczy.mytv.tv.R
import androidx.compose.ui.res.stringResource

@Composable
fun UpdateScreen(
    modifier: Modifier = Modifier,
    updateViewModel: UpdateViewModel = updateVM,
    onBackPressed: () -> Unit = {},
) {
    val latestFile by lazy { File(Globals.cacheDir, "latest.apk") }
    val context = LocalContext.current
    val coroutineScope = rememberCoroutineScope()
    val latestRelease = updateViewModel.latestRelease

    val (hasPermission, requestPermission) = rememberCanRequestPackageInstallsPermission()

    LaunchedEffect(hasPermission) {
        if (hasPermission) ApkInstaller.installApk(context, latestFile.path)
    }

    LaunchedEffect(updateViewModel.updateDownloaded) {
        if (!updateViewModel.updateDownloaded) return@LaunchedEffect

        if (hasPermission) ApkInstaller.installApk(context, latestFile.path)
        else requestPermission()
    }
    AppScreen(modifier = modifier,
        onBackPressed = onBackPressed,
    ) {
        Box(
            modifier = Modifier
                .fillMaxSize()
                .padding(SAFE_AREA_HORIZONTAL_PADDING.dp),
        ) {
            Row(
                modifier = Modifier.align(Alignment.Center),
                horizontalArrangement = Arrangement.spacedBy(2.gridColumns()),
            ) {
                Column(
                    modifier = Modifier.width(5.gridColumns()),
                    verticalArrangement = Arrangement.spacedBy(16.dp),
                ) {
                    Text(
                        stringResource(R.string.ui_update_latest_version, latestRelease.version),
                        style = MaterialTheme.typography.headlineMedium,
                    )

                    LazyColumn {
                        item {
                            Text(
                                latestRelease.description,
                                style = MaterialTheme.typography.bodyLarge
                            )
                        }
                    }
                }
                Column(
                    verticalArrangement = Arrangement.spacedBy(16.dp, Alignment.CenterVertically) 
                ) {
                    if (updateViewModel.isUpdateAvailable) {
                        if (updateViewModel.isUpdating) {
                            UpdateActionBtn(
                                modifier = Modifier.focusOnLaunched(),
                                title = stringResource(R.string.ui_update_updating),
                            )
                        } else {
                            UpdateActionBtn(
                                modifier = Modifier.focusOnLaunched(),
                                title = stringResource(R.string.ui_update_now),
                                onSelected = {
                                    coroutineScope.launch(Dispatchers.IO) {
                                        updateViewModel.downloadAndUpdate(latestFile)
                                    }
                                },
                            )
                        }

                        UpdateActionBtn(
                            title = stringResource(R.string.ui_update_ignore_and_back),
                            onSelected = onBackPressed,
                        )
                    }
                    else {
                        UpdateActionBtn(
                            title = stringResource(R.string.ui_update_is_latest),
                            onSelected = onBackPressed,
                        )
                    }
                }
            }
        }
    }
}

@Composable
private fun UpdateActionBtn(
    modifier: Modifier = Modifier,
    title: String,
    onSelected: () -> Unit = {},
) {
    ListItem(
        modifier = modifier
            .width(4.gridColumns())
            .handleKeyEvents(onSelect = onSelected),
        onClick = { },
        selected = false,
        headlineContent = { Text(title) },
    )
}

@Preview(device = "id:Android TV (720p)")
@Composable
private fun UpdateScreenPreview() {
    MyTvTheme {
        UpdateScreen(
            updateViewModel = UpdateViewModel(
                debugLatestRelease = GitRelease(
                    version = "9.0.0",
                    description = " 移除自定义订阅源界面获取订阅源信息，可能导致部分低内存设备OOM\r\n\r\n"
                        .repeat(20),
                )
            )
        )

        PreviewWithLayoutGrids { }
    }
}