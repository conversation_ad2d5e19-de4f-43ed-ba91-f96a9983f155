郑重提醒：我们不反对基于本应用进行私人DIY，但修改包名和版本号会为开发工作带来阻碍，你应当避免修改和使用修改上述数据的应用，否则后果自负。

由于VLC播放器的引入，我们对应用的更新策略作了调整。因此当你从旧版本升级时，可能会升级到armv7a的版本，但它会在之后的版本中正确升级到你系统对应ABI的版本。

# 自上一测试版以来更新的功能
- 优化界面
- 修复bug
- 优化设置界面和播放器进度界面
- 优化回放相关逻辑 @getdo
- 更新依赖

# 自上一稳定版以来更新的功能
- 更新和优化界面
- 更新图标
- 修复DRM key解析的问题
- 添加VLC播放器
- 默认UA由okhttp变更为Mytv.Android
- 优化EPG格式检测方式
- 优化订阅源加载切换相关逻辑
- 添加了对阿拉伯语的支持 @odehalaa
- 优化播放器参数
- 优化Webview
- 优化更新方式
- ijk RTSP 支持选择协议 @getdo
- 订阅源和图标支持Asset链接：file:///android_asset/*
- 修复其他错误
