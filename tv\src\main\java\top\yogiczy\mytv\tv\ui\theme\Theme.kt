package top.yogiczy.mytv.tv.ui.theme

import android.os.Build
import androidx.compose.foundation.isSystemInDarkTheme
import androidx.compose.runtime.Composable
import androidx.compose.runtime.CompositionLocalProvider
import androidx.compose.material3.ExperimentalMaterial3ExpressiveApi
import androidx.tv.material3.LocalContentColor
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.MaterialExpressiveTheme
import androidx.compose.material3.MotionScheme
import androidx.compose.ui.platform.LocalContext
import androidx.compose.material3.dynamicDarkColorScheme
import androidx.compose.material3.dynamicLightColorScheme
import top.yogiczy.mytv.core.designsystem.theme.Colors
import top.yogiczy.mytv.core.designsystem.theme.LocalColors
import top.yogiczy.mytv.core.designsystem.theme.darkColors
import top.yogiczy.mytv.core.designsystem.theme.lightColors

@OptIn(ExperimentalMaterial3ExpressiveApi::class)
@Composable
fun MyTvTheme(
    isInDarkTheme: Boolean = isSystemInDarkTheme(),
    content: @Composable () -> Unit,
) {
    val dynamicColor = Build.VERSION.SDK_INT >= Build.VERSION_CODES.S
    val colorScheme = when {
        dynamicColor && isInDarkTheme -> dynamicDarkColorScheme(LocalContext.current)
        dynamicColor && !isInDarkTheme -> dynamicLightColorScheme(LocalContext.current)
        isInDarkTheme -> colorSchemeForDarkMode
        else -> colorSchemeForLightMode
    }
    // val colors = when {
    //     isInDarkTheme -> darkColors
    //     else -> lightColors
    // }

    // MaterialTheme(
    MaterialExpressiveTheme(
        colorScheme = colorScheme,
        motionScheme = MotionScheme.expressive(),
        typography = Typography,
    ) {
        CompositionLocalProvider(
            LocalContentColor provides MaterialTheme.colorScheme.onSurface,
            // LocalColors provides colors,
        ) {
            content()
        }
    }
}

val MaterialTheme.colors: Colors
    @Composable
    get() = LocalColors.current

const val SAFE_AREA_HORIZONTAL_PADDING = 58
const val SAFE_AREA_VERTICAL_PADDING = 24
const val LAYOUT_GRID_SPACING = 20
const val LAYOUT_GRID_WIDTH = 52
const val LAYOUT_GRID_COLUMNS = 12
const val DESIGN_WIDTH = 960
const val DESIGN_HEIGHT = 540
