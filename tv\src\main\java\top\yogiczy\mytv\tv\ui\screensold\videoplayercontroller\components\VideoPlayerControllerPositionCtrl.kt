package top.yogiczy.mytv.tv.ui.screensold.videoplayercontroller.components

import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.layout.width
import androidx.compose.material3.ExperimentalMaterial3ExpressiveApi
import androidx.compose.material3.MaterialShapes
import androidx.compose.material3.toShape
import androidx.compose.runtime.Composable
import androidx.compose.runtime.Stable
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.getValue
import androidx.compose.runtime.setValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.draw.clip
import androidx.compose.ui.Modifier
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import androidx.compose.ui.focus.FocusDirection
import androidx.compose.ui.input.key.Key
import androidx.compose.ui.input.key.key
import androidx.compose.ui.input.key.KeyEventType
import androidx.compose.ui.input.key.type
import androidx.compose.ui.input.key.onPreviewKeyEvent
import androidx.compose.ui.platform.LocalFocusManager
import androidx.tv.material3.Text
import top.yogiczy.mytv.tv.ui.material.Slider
import top.yogiczy.mytv.tv.ui.theme.MyTvTheme
import java.text.SimpleDateFormat
import java.util.Locale
import kotlin.math.max
import kotlin.math.min

@OptIn(ExperimentalMaterial3ExpressiveApi::class)
@Composable
fun VideoPlayerControllerPositionCtrl(
    modifier: Modifier = Modifier,
    currentPositionProvider: () -> Long = { 0L },
    durationProvider: () -> Pair<Long, Long> = { 0L to 0L },
    seekToPositionProvider: () -> Long? = { null },
    seekTo: (Long) -> Unit = {},
) {

    VideoPlayerControllerPositionProgress(
        modifier = Modifier.padding(start = 10.dp),
        currentPositionProvider = { seekToPositionProvider() ?: currentPositionProvider() },
        durationProvider = durationProvider,
        seekToPositionProvider = seekToPositionProvider,
        onProcessUpdate = { progress -> seekTo(progress.toLong()) },
    )
}

@Composable
private fun VideoPlayerControllerPositionProgress(
    modifier: Modifier = Modifier,
    currentPositionProvider: () -> Long = { 0L },
    durationProvider: () -> Pair<Long, Long> = { 0L to 0L },
    seekToPositionProvider: () -> Long? = { null },
    onProcessUpdate: (Float) -> Unit = {},
) {
    val currentPosition = currentPositionProvider()
    val duration = durationProvider()
    val timeFormat = SimpleDateFormat("HH:mm:ss", Locale.getDefault())
    val focusManager = LocalFocusManager.current
    val now = System.currentTimeMillis()
    val isAbsolute = duration.first > 0L && duration.second > 0L
    val toleranceMs = 2000L
    val isLive = isAbsolute && duration.second >= now - toleranceMs
    val isSeeking = seekToPositionProvider() != null
    var liveEndAnchor by remember { mutableStateOf<Long?>(null) }
    var liveStartAnchor by remember { mutableStateOf<Long?>(null) }
    if (isLive) {
        if (isSeeking && liveEndAnchor == null) {
            liveEndAnchor = duration.second
            liveStartAnchor = duration.first
        }
        // Keep anchors after seeking to preserve the original interval visualization
    }
    val startTimeMs = liveStartAnchor ?: duration.first
    val endTimeMs = liveEndAnchor ?: duration.second
    val rightTimeMs = endTimeMs
    val leftTimeMs = if (isLive) {
        if (isSeeking) currentPosition else rightTimeMs
    } else currentPosition

    Row(
        modifier = modifier
            .onPreviewKeyEvent {
                if (it.type == KeyEventType.KeyDown) {
                    when (it.key) {
                        Key.DirectionUp -> {
                            focusManager.moveFocus(FocusDirection.Up)
                            true 
                        }
                        Key.DirectionDown -> {
                            focusManager.moveFocus(FocusDirection.Down)
                            true
                        }
                        else -> false
                    }
                } else {
                    false
                }
            },
        horizontalArrangement = Arrangement.spacedBy(8.dp),
        verticalAlignment = Alignment.CenterVertically,
    ) {
        val sliderFirst = startTimeMs
        val sliderLast = endTimeMs
        val sliderCurrentAbs = currentPosition.coerceIn(sliderFirst, sliderLast)

        // Map to a relative range to avoid float precision issues with epoch millis
        val sliderRange = max(1L, sliderLast - sliderFirst)
        val sliderCurrentRel = (sliderCurrentAbs - sliderFirst).coerceIn(0L, sliderRange)

        Slider(
            first = 0f,
            current = sliderCurrentRel * 1f,
            last = sliderRange * 1f,
            modifier = Modifier
                .weight(1f)
                .height(5.dp),
            onProcessUpdate = { rel -> onProcessUpdate((sliderFirst + rel.toLong()) * 1f) },
        )

        Text(
            text = "${timeFormat.format(leftTimeMs)} / ${timeFormat.format(rightTimeMs)}",
        )
    }
}