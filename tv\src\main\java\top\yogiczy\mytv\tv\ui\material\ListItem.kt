package top.yogiczy.mytv.tv.ui.material

import androidx.compose.runtime.Composable
import androidx.compose.foundation.interaction.MutableInteractionSource
import androidx.compose.foundation.layout.BoxScope
import androidx.compose.material3.MaterialTheme
import androidx.compose.ui.Modifier
import androidx.tv.material3.ListItem
import androidx.tv.material3.ListItemDefaults
import androidx.tv.material3.ListItemBorder
import androidx.tv.material3.ListItemColors
import androidx.tv.material3.ListItemShape
import androidx.tv.material3.ListItemScale
import androidx.tv.material3.ListItemGlow
import androidx.compose.ui.unit.Dp

@Composable
fun ListItem(
    selected: Boolean,
    onClick: () -> Unit,
    headlineContent: @Composable () -> Unit,
    modifier: Modifier = Modifier,
    enabled: Boolean = true,
    onLongClick: (() -> Unit)? = null,
    overlineContent: (@Composable () -> Unit)? = null,
    supportingContent: (@Composable () -> Unit)? = null,
    leadingContent: (@Composable BoxScope.() -> Unit)? = null,
    trailingContent: (@Composable () -> Unit)? = null,
    tonalElevation: Dp = ListItemDefaults.TonalElevation,
    shape: ListItemShape = ListItemDefaults.shape(),
    colors: ListItemColors? = null,
    scale: ListItemScale = ListItemDefaults.scale(),
    border: ListItemBorder = ListItemDefaults.border(),
    glow: ListItemGlow = ListItemDefaults.glow(),
    interactionSource: MutableInteractionSource? = null
){
    val applyColors = colors ?: ListItemDefaults.colors(
        containerColor = MaterialTheme.colorScheme.surfaceContainerHighest.copy(alpha = 0f),
        contentColor = MaterialTheme.colorScheme.onSurface,
        focusedContainerColor = MaterialTheme.colorScheme.primary.copy(alpha = 0.9f),
        focusedContentColor = MaterialTheme.colorScheme.onPrimary,
        selectedContainerColor = MaterialTheme.colorScheme.primary.copy(alpha = 0.9f),
        selectedContentColor = MaterialTheme.colorScheme.onPrimary,
        pressedContainerColor = MaterialTheme.colorScheme.primary.copy(alpha = 0.9f),
        pressedContentColor = MaterialTheme.colorScheme.onPrimary,
        focusedSelectedContainerColor = MaterialTheme.colorScheme.primary.copy(alpha = 0.9f),
        focusedSelectedContentColor = MaterialTheme.colorScheme.onPrimary,
        pressedSelectedContainerColor = MaterialTheme.colorScheme.primary.copy(alpha = 0.9f),
        pressedSelectedContentColor = MaterialTheme.colorScheme.onPrimary,
    )
    ListItem(
        selected = selected,
        onClick = onClick,
        headlineContent = headlineContent,
        modifier = modifier,
        enabled = enabled,
        onLongClick = onLongClick,
        overlineContent = overlineContent,
        supportingContent = supportingContent,
        leadingContent = leadingContent,
        trailingContent = trailingContent,
        tonalElevation = tonalElevation,
        shape = shape,
        colors = applyColors,
        scale = scale,
        border = border,
        glow = glow,
        interactionSource = interactionSource
    )
}