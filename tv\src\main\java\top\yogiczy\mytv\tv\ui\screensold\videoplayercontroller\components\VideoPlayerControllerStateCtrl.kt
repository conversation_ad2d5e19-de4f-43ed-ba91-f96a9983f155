package top.yogiczy.mytv.tv.ui.screensold.videoplayercontroller.components

import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.size
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.ChevronLeft
import androidx.compose.material.icons.filled.ChevronRight
import androidx.compose.material.icons.filled.KeyboardDoubleArrowLeft
import androidx.compose.material.icons.filled.KeyboardDoubleArrowRight
import androidx.compose.material.icons.filled.Pause
import androidx.compose.material.icons.filled.PlayArrow
import androidx.compose.material3.ExperimentalMaterial3ExpressiveApi
import androidx.compose.material3.MaterialShapes
import androidx.compose.material3.toShape
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import androidx.tv.material3.Icon
import androidx.tv.material3.LocalContentColor
import top.yogiczy.mytv.tv.ui.material.ProgressIndicator
import top.yogiczy.mytv.tv.ui.theme.MyTvTheme

@OptIn(ExperimentalMaterial3ExpressiveApi::class)
@Composable
fun VideoPlayerControllerStateCtrl(
    modifier: Modifier = Modifier,
    isPlayingProvider: () -> Boolean = { false },
    isBufferingProvider: () -> Boolean = { false },
    onPlay: () -> Unit = {},
    onPause: () -> Unit = {},
    seekForward: (Long) -> Unit = {},
    seekNext: (Long) -> Unit = {},
) {
    val isPlaying = isPlayingProvider()
    val isBuffering = isBufferingProvider()
    Row(
        modifier = modifier,
        horizontalArrangement = Arrangement.spacedBy(8.dp),
        verticalAlignment = Alignment.CenterVertically,
    ) {
        VideoPlayerControllerBtn(
            modifier = Modifier.size(36.dp),
            shape = MaterialShapes.Cookie9Sided.toShape(),
            focusedShape = MaterialShapes.Cookie12Sided.toShape(),
            pressedShape = MaterialShapes.Clover4Leaf.toShape(),
            imageVector = Icons.Default.KeyboardDoubleArrowLeft,
            onSelect = { seekForward(1000L * 60) },
        )
        VideoPlayerControllerBtn(
            modifier = Modifier.size(30.dp),
            shape = MaterialShapes.Cookie6Sided.toShape(),
            focusedShape = MaterialShapes.Cookie12Sided.toShape(),
            pressedShape = MaterialShapes.Clover4Leaf.toShape(),
            imageVector = Icons.Default.ChevronLeft,
            onSelect = { seekForward(1000L * 10) },
        )
        VideoPlayerControllerBtn(
            modifier = modifier.size(42.dp),
            shape = MaterialShapes.Circle.toShape(),
            focusedShape = MaterialShapes.Sunny.toShape(),
            pressedShape = MaterialShapes.VerySunny.toShape(),
            onSelect = {
                if (!isBuffering) {
                    if (isPlaying) onPause()
                    else onPlay()
                }
            },
        ) {
            if (isBuffering) {
                ProgressIndicator(
                    modifier = Modifier.size(32.dp),
                    color = LocalContentColor.current,
                )
                // CircularProgressIndicator(
                //     modifier = Modifier.size(20.dp),
                //     strokeWidth = 3.dp,
                //     color = LocalContentColor.current,
                //     trackColor = Color.Transparent,
                // )
            } else {
                Icon(
                    imageVector = if (isPlaying) Icons.Default.Pause else Icons.Default.PlayArrow,
                    contentDescription = null,
                )
            }
        }
        VideoPlayerControllerBtn(
            modifier = Modifier.size(30.dp),
            shape = MaterialShapes.Cookie6Sided.toShape(),
            focusedShape = MaterialShapes.Cookie12Sided.toShape(),
            pressedShape = MaterialShapes.Clover4Leaf.toShape(),
            imageVector = Icons.Default.ChevronRight,
            onSelect = { seekNext(1000L * 10) },
        )
        VideoPlayerControllerBtn(
            modifier = Modifier.size(36.dp),
            shape = MaterialShapes.Cookie9Sided.toShape(),
            focusedShape = MaterialShapes.Cookie12Sided.toShape(),
            pressedShape = MaterialShapes.Clover4Leaf.toShape(),
            imageVector = Icons.Default.KeyboardDoubleArrowRight,
            onSelect = { seekNext(1000L * 60) },
        )
    }
}

@Preview
@Composable
private fun VideoPlayerControllerStateCtrlPreview() {
    MyTvTheme {

        Row(
            horizontalArrangement = Arrangement.spacedBy(8.dp),
            verticalAlignment = Alignment.CenterVertically,
        ) {
            VideoPlayerControllerStateCtrl(
                isPlayingProvider = { false },
            )

            VideoPlayerControllerStateCtrl(
                isPlayingProvider = { true },
            )

            VideoPlayerControllerStateCtrl(
                isBufferingProvider = { true },
            )
        }
    }
}