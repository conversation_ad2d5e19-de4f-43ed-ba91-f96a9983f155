package top.yogiczy.mytv.tv.ui.screen.dashboard.components

import androidx.compose.foundation.layout.padding
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableLongStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.setValue
import androidx.compose.ui.Modifier
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import androidx.compose.material3.ElevatedSuggestionChip
import androidx.tv.material3.Text
import kotlinx.coroutines.delay
import top.yogiczy.mytv.tv.ui.theme.MyTvTheme
import java.text.SimpleDateFormat
import java.util.Locale

@Composable
fun DashboardTime(modifier: Modifier = Modifier) {
    var timestamp by remember { mutableLongStateOf(System.currentTimeMillis()) }
    LaunchedEffect(Unit) {
        while (true) {
            delay(1000)
            timestamp = System.currentTimeMillis()
        }
    }

    val timeFormat = SimpleDateFormat("MM/dd EEE HH:mm:ss", Locale.getDefault())
    ElevatedSuggestionChip(
        onClick = { /* Handle click */ },
        modifier = modifier,
        label = {
            Text(text = timeFormat.format(timestamp), modifier = Modifier.padding(horizontal = 16.dp, vertical = 8.dp))
        }
    ) 
}

@Preview
@Composable
private fun DashboardTimePreview() {
    MyTvTheme {
        DashboardTime()
    }
}