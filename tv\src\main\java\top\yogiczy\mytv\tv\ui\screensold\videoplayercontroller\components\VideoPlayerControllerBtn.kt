package top.yogiczy.mytv.tv.ui.screensold.videoplayercontroller.components

import androidx.compose.runtime.Composable
import androidx.compose.foundation.shape.CircleShape
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Shape
import androidx.compose.ui.graphics.vector.ImageVector
import androidx.compose.material3.ExperimentalMaterial3ExpressiveApi
import androidx.tv.material3.Icon
import androidx.tv.material3.IconButton
import androidx.tv.material3.IconButtonDefaults
import top.yogiczy.mytv.tv.ui.utils.handleKeyEvents

@OptIn(ExperimentalMaterial3ExpressiveApi::class)
@Composable
fun VideoPlayerControllerBtn(
    modifier: Modifier = Modifier,
    shape: Shape = CircleShape,
    focusedShape: Shape = CircleShape,
    pressedShape: Shape = CircleShape,
    onSelect: () -> Unit = {},
    content: @Composable () -> Unit,
) {
    IconButton(
        modifier = modifier
            .handleKeyEvents(onSelect = onSelect),
        shape = IconButtonDefaults.shape(shape, focusedShape, pressedShape),
        onClick = {},
    ) {
        content()
    }
}

@OptIn(ExperimentalMaterial3ExpressiveApi::class)
@Composable
fun VideoPlayerControllerBtn(
    modifier: Modifier = Modifier,
    imageVector: ImageVector,
    shape: Shape = CircleShape,
    focusedShape: Shape = CircleShape,
    pressedShape: Shape = CircleShape,
    onSelect: () -> Unit = {},
) {
    VideoPlayerControllerBtn(
        modifier = modifier,
        shape = shape,
        focusedShape = focusedShape,
        pressedShape = pressedShape,
        onSelect = onSelect,
        content = { Icon(imageVector = imageVector, contentDescription = null) },
    )
}