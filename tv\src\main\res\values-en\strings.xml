<resources>
    <string name="app_name">TV Live</string>
    <string name="codec_detail_audio_bitrate_range_title">Audio Bitrate Range</string>
    <string name="codec_detail_color_formats_title">Color Formats</string>
    <string name="codec_detail_hs_hardware">Hardware Decoder</string>
    <string name="codec_detail_hs_software">Software Decoder</string>
    <string name="codec_detail_hs_title">Hardware/Software Decoder</string>
    <string name="codec_detail_max_supported_instances_title">Max Concurrent Codec Instances</string>
    <string name="codec_detail_video_frame_achievable_title">Achievable Video Frame Rate (may be unavailable)</string>
    <string name="codec_detail_video_frame_range_title">Video Frame Rate Range</string>
    <string name="codec_detail_video_frame_supported_title">Supported Video Frame Rate</string>
    <string name="codec_detail_video_frame_unsupported">Not Supported</string>
    <string name="codec_detail_video_max_bitrate_title">Max Video Bitrate</string>
    <string name="codec_detail_video_resolution_1080p">1080P</string>
    <string name="codec_detail_video_resolution_1440p">2K</string>
    <string name="codec_detail_video_resolution_2160p">4K</string>
    <string name="codec_detail_video_resolution_360p">360P</string>
    <string name="codec_detail_video_resolution_4320p">8K</string>
    <string name="codec_detail_video_resolution_480p">480P</string>
    <string name="codec_detail_video_resolution_720p">720P</string>
    <string name="codec_detail_video_resolution_unknown">Unknown</string>
    <string name="ui_welcome_sentence0">Welcome to use</string>
    <string name="ui_welcome_sentence1">. Please read the following carefully before using:</string>
    <string name="ui_welcome_sentence2">1. This software is for learning and communication purposes only and is prohibited from any commercial use. You may not edit or modify this software for the second time.</string>
    <string name="ui_welcome_sentence3">2. This software does not provide any live content. All live content comes from the Internet.</string>
    <string name="ui_welcome_sentence4">3. You use this software entirely at your own discretion and are fully responsible for your actions and all consequences.</string>
    <string name="ui_welcome_sentence5">4. If this software infringes your legitimate rights and interests, please contact the author in time. The author will promptly remove the relevant content.</string>
    <string name="ui_welcome_sentence6">By continuing to use this software, you fully understand and agree to the above content.</string>
    <string name="ui_agreement_title">User Notice</string>
    <string name="ui_agreement_agree">Read and Agree</string>
    <string name="ui_agreement_disagree">Exit</string>
    <string name="ui_dashboard_module_live">Live</string>
    <string name="ui_dashboard_module_all_channels">All Channels</string>
    <string name="ui_dashboard_module_favorites">Favorites</string>
    <string name="ui_dashboard_module_search">Search</string>
    <string name="ui_dashboard_module_multi_view">Multi-view</string>
    <string name="ui_dashboard_module_push">Web Panel</string>
    <string name="ui_dashboard_module_settings">Settings</string>
    <string name="ui_dashboard_module_about">About</string>
    <string name="ui_dashboard_module_recently_watched">Recent Channel</string>
    <string name="ui_favorites_clear">Clear</string>
    <string name="ui_channel_group_all">All</string>
    <string name="ui_channel_info_time_shift">Time Shift</string>
    <string name="ui_channel_info_replay">Replay</string>
    <string name="ui_channel_info_ready">Ready</string>
    <string name="ui_channel_info_loading">Loading...</string>
    <string name="ui_channel_info_load_failed">Load Failed</string>
    <string name="ui_channel_info_favorite_cancel">Unfavorite:</string>
    <string name="ui_channel_info_favorite_add">Favorited:</string>
    <string name="ui_channel_info_favorite_clear">All favorites cleared</string>
    <string name="ui_channel_info_update_checking">Checking for updates...</string>
    <string name="ui_channel_info_update_found">New version found: </string>
    <string name="ui_channel_info_update_latest">Already the latest version</string>
    <string name="ui_channel_info_exit_live">Press again to exit live</string>
    <string name="ui_app_exit">Press again to exit</string>
    <string name="ui_cache_cleared">Cache cleared</string>
    <string name="ui_cloud_sync_pull">Pull cloud data</string>
    <string name="ui_iptv_source_loading">Loading subscription source</string>
    <string name="ui_iptv_channel_merge">Merge similar channels</string>
    <string name="ui_iptv_channel_hybrid">Match available web sources</string>
    <string name="ui_iptv_channel_epg_failed">Failed to get EPG, please check network connection</string>
    <string name="ui_multi_view_max_count_exceeded">Exceeded maximum number of channels:</string>
    <string name="ui_multi_view_channel_exists">Channel already exists</string>
    <string name="ui_multi_view_channel_minimum">Keep at least one channel</string>
    <string name="ui_push_service_started">Service started:</string>
    <string name="ui_push_scan_qr">Please scan the QR code or enter the address to connect</string>
    <string name="ui_search_keyword_hint">Keyword...</string>
    <string name="ui_crash_handler_app_crashed">App crashed</string>
    <string name="ui_crash_handler_copy_log">Copy Log</string>
    <string name="ui_crash_handler_crash_log">Crash Log</string>
    <string name="ui_crash_handler_crash_log_copied">Crash log copied to clipboard</string>
    <string name="ui_crash_handler_restart">Restart</string>
    <string name="ui_crash_handler_tip">Tip: You can click the "Copy Log" button above to copy the full crash information for reporting issues.</string>
    <string name="ui_return">Return</string>
    <string name="ui_excellent_program">Featured Program</string>
    <string name="ui_minutes">Minutes</string>
    <string name="ui_favorites_empty">No favorite channels</string>
    <string name="ui_channel_empty">Channel list not loaded yet</string>
    <string name="ui_channel_view_schedule">View EPG to the right</string>
    <string name="ui_channel_view_subscription_left">View sources to the left</string>
    <string name="ui_channel_view_source">Source</string>
    <string name="ui_channel_view_epg">EPG</string>
    <string name="ui_channel_view_route">Line</string>
    <string name="ui_channel_view_playback_control">Playback Control</string>
    <string name="ui_channel_view_player1">Player:</string>
    <string name="ui_channel_view_video_track">Video Track</string>
    <string name="ui_channel_view_audio_track">Audio Track</string>
    <string name="ui_channel_view_subtitle">Subtitle</string>
    <string name="ui_channel_view_display_mode">Display Mode</string>
    <string name="ui_channel_view_clear_cache">Clear Cache</string>
    <string name="ui_channel_view_home">Home</string>
    <string name="ui_channel_view_force_soft_decode">Software Decode</string>
    <string name="ui_channel_view_auto_decode">Auto</string>
    <string name="ui_channel_view_refresh">Refresh</string>
    <string name="ui_channel_view_general">General</string>
    <string name="ui_channel_view_interface">Interface</string>
    <string name="ui_channel_view_theme">Theme</string>
    <string name="ui_channel_view_control">Control</string>
    <string name="ui_channel_view_player">Player</string>
    <string name="ui_channel_view_update">Update</string>
    <string name="ui_channel_view_network">Network</string>
    <string name="ui_channel_view_cloud_sync">Cloud Sync</string>
    <string name="ui_channel_view_permissions">Permissions</string>
    <string name="ui_channel_view_debug">Debug</string>
    <string name="ui_channel_view_log">Log</string>
    <string name="ui_channel_view_boot_start">Boot Start</string>
    <string name="ui_channel_view_boot_start_support">Please ensure your device supports this feature</string>
    <string name="ui_channel_view_boot_start_live">Enter live directly on startup</string>
    <string name="ui_channel_view_picture_in_picture">Picture-in-Picture</string>
    <string name="ui_channel_view_clear_cache_support">About</string>
    <string name="ui_channel_view_restore_initialization">Restore Initialization</string>
    <string name="ui_channel_view_restore_initialization_support">Initialization restored</string>
    <string name="cloud_sync_pull_failed">Failed to pull cloud data</string>
    <string name="cloud_sync_pull">Pull from Cloud</string>
    <string name="cloud_sync_push">Push to Cloud</string>
    <string name="cloud_sync_push_success">Push to cloud succeeded</string>
    <string name="cloud_sync_push_failed">Push to cloud failed</string>
    <string name="cloud_sync_data">Cloud Data</string>
    <string name="cloud_sync_data_long_press">Long press to apply current cloud data</string>
    <string name="cloud_sync_no_data">No cloud data</string>
    <string name="cloud_sync_version">Cloud Version</string>
    <string name="cloud_sync_push_time">Push Time</string>
    <string name="cloud_sync_push_device">Push Device</string>
    <string name="cloud_sync_description">Description</string>
    <string name="cloud_sync_apply_success">Cloud data applied successfully</string>
    <string name="cloud_sync_auto_pull">Auto Pull</string>
    <string name="cloud_sync_auto_pull_desc">Automatically pull and apply cloud data on app launch</string>
    <string name="cloud_sync_provider">Cloud Sync Provider</string>
    <string name="cloud_sync_github_gist_id">Github Gist Id</string>
    <string name="cloud_sync_github_gist_token">Github Gist Token</string>
    <string name="cloud_sync_gitee_gist_id">Gitee Snippet Id</string>
    <string name="cloud_sync_gitee_gist_token">Gitee Snippet Token</string>
    <string name="cloud_sync_network_url">Network URL</string>
    <string name="cloud_sync_local_file_path">Local File Path</string>
    <string name="cloud_sync_webdav_url">WebDAV Address</string>
    <string name="cloud_sync_webdav_username">WebDAV Username</string>
    <string name="cloud_sync_webdav_password">WebDAV Password</string>
    <string name="ui_channel_no_select">Channel Number Selection</string>
    <string name="ui_channel_no_select_desc">Select channels using number keys</string>
    <string name="ui_channel_list_loop">Channel List Loop</string>
    <string name="ui_channel_list_loop_desc">When enabled, reaching the end of the list will loop to the other end</string>
    <string name="ui_channel_change_cross_group">Channel Switch Across Groups</string>
    <string name="ui_channel_change_cross_group_desc">When enabled, up/down keys switch between all channels; when disabled, only within the current group</string>
    <string name="ui_control_action_settings">Key (Gesture) Actions</string>
    <string name="ui_control_action_settings_desc">Customize key/gesture actions in the playback interface</string>
    <string name="ui_debug_show_fps">Show FPS</string>
    <string name="ui_debug_show_fps_desc">Display FPS and bar chart at the top left corner</string>
    <string name="ui_debug_show_player_metadata">Show Player Info</string>
    <string name="ui_debug_show_player_metadata_desc">Show detailed player info (codec, decoder, sample rate, etc.)</string>
    <string name="ui_debug_show_layout_grids">Show Layout Grids</string>
    <string name="ui_debug_decoder_info">Decoder Info</string>
    <string name="ui_debug_decoder_info_desc">View system decoders</string>
    <string name="ui_epg_enable">Enable EPG</string>
    <string name="ui_epg_enable_desc">May be slow on first load</string>
    <string name="ui_epg_source_follow_iptv">Follow Subscription Source</string>
    <string name="ui_epg_source_follow_iptv_desc">Prefer EPG defined in the subscription source</string>
    <string name="ui_epg_source_custom">Custom EPG</string>
    <string name="ui_epg_refresh_time_threshold">Refresh Time Threshold</string>
    <string name="ui_epg_refresh_time_threshold_desc">EPG will not refresh until %1$d:00</string>
    <string name="ui_custom_subscription_source">Custom Subscription Source</string>
    <string name="ui_subscription_source_cache_time">Subscription Source Cache Time</string>
    <string name="ui_subscription_source_cache_time_none">No Cache</string>
    <string name="ui_subscription_source_cache_time_forever">Forever</string>
    <string name="ui_channel_group_manage">Channel Group Management</string>
    <string name="ui_channel_group_count">%1$d groups in total</string>
    <string name="ui_channel_group_count_hidden">%1$d groups in total, %2$d hidden</string>
    <string name="ui_channel_alias">Channel Alias</string>
    <string name="ui_channel_alias_count">%1$d channels, %2$d aliases</string>
    <string name="ui_similar_channel_merge">Merge Similar Channels</string>
    <string name="ui_similar_channel_merge_desc">Channels with the same alias will be merged</string>
    <string name="ui_channel_logo_provider">Channel Logo Provider</string>
    <string name="ui_channel_logo_override">Channel Logo Override</string>
    <string name="ui_channel_logo_override_desc">Override channel logos defined in the subscription source with those from the logo provider</string>
    <string name="ui_iptv_pltv_to_tvod">PLTV to TVOD</string>
    <string name="ui_iptv_pltv_to_tvod_desc">Automatically replace PLTV with TVOD in subscription links to support playback</string>
    <string name="ui_auto_add_web_source">Auto Add Web Source</string>
    <string name="ui_auto_add_web_source_desc">Automatically add corresponding web source lines for channels in the subscription source</string>
    <string name="ui_iptv_hybrid_yangshipin_cookie">Yangshipin Web Source Cookie</string>
    <string name="ui_iptv_hybrid_yangshipin_cookie_desc">Log in to Yangshipin to watch paid channels</string>
    <string name="ui_network_retry_count">HTTP Request Retry Count</string>
    <string name="ui_network_retry_count_desc">Affects fetching subscription and EPG data</string>
    <string name="ui_network_retry_interval">HTTP Request Retry Interval</string>
    <string name="ui_network_retry_interval_desc">Affects fetching subscription and EPG data</string>
    <string name="ui_permission_install_other_apps">Install Unknown Apps</string>
    <string name="ui_permission_read_external_storage">Read External Storage / Manage All Files</string>
    <string name="ui_theme_reset_to_default">Reset to Default</string>
    <string name="ui_show_epg_programme_progress">EPG Progress</string>
    <string name="ui_show_epg_programme_progress_desc">Show current program progress bar at the bottom of the channel</string>
    <string name="ui_show_epg_programme_permanent_progress">Permanent EPG Progress</string>
    <string name="ui_show_epg_programme_permanent_progress_desc">Show current program progress bar at the bottom of the player</string>
    <string name="ui_show_channel_logo">Show Channel Logo</string>
    <string name="ui_show_channel_preview">Channel Preview</string>
    <string name="ui_use_classic_panel_screen">Classic Channel Selection Screen</string>
    <string name="ui_use_classic_panel_screen_desc">Replace the channel selection screen with the classic three-section structure</string>
    <string name="ui_classic_show_source_list">Show Subscription Source List</string>
    <string name="ui_classic_show_source_list_desc">Enable "View sources to the left" in the classic channel selection screen</string>
    <string name="ui_classic_show_channel_info">Show Channel Info</string>
    <string name="ui_classic_show_channel_info_desc">Show detailed info of the current channel in the classic channel selection screen</string>
    <string name="ui_classic_show_all_channels">Show All Channels</string>
    <string name="ui_classic_show_all_channels_desc">Show all channels of the current subscription source</string>
    <string name="ui_time_show_mode">Time Display</string>
    <string name="ui_screen_auto_close_delay">Auto Close Screen Timeout</string>
    <string name="ui_screen_auto_close_delay_never">Never Close</string>
    <string name="ui_density_scale_ratio">UI Scale Ratio</string>
    <string name="ui_density_scale_ratio_auto">Auto</string>
    <string name="ui_font_scale_ratio">Font Scale Ratio</string>
    <string name="ui_video_player_subtitle_settings">Subtitle Settings</string>
    <string name="ui_video_player_subtitle_settings_desc">Subtitle style adjustment</string>
    <string name="ui_focus_optimize">Focus Optimization</string>
    <string name="ui_focus_optimize_desc">Disable to fix crashes on touch devices in some scenarios</string>
    <string name="iptv_channel_favorite_enable">Enable Favorites</string>
    <string name="iptv_channel_favorite_enable_desc">Show favorite channel list for the current subscription source</string>
    <string name="ui_channel_view_update_channel">Update Channel</string>
    <string name="ui_channel_view_stable">Stable</string>
    <string name="ui_channel_view_beta">Beta</string>
    <string name="ui_channel_view_dev">Dev</string>
    <string name="ui_channel_view_force_remind">Force Update Reminder</string>
    <string name="ui_channel_view_force_remind_on">Full screen reminder when a new version is detected</string>
    <string name="ui_channel_view_force_remind_off">Only message reminder when a new version is detected</string>
    <string name="ui_player_view_player">Video Player</string>
    <string name="ui_player_view_player_core">Video Player Core</string>
    <string name="ui_player_view_render_mode">Render Mode</string>
    <string name="ui_player_view_force_soft_decode">Force Software Decode</string>
    <string name="ui_player_view_force_soft_decode_desc">For Media3, use device and extension software decoders;\n For IJK, disable MediaCodec decoding (use ffmpeg)</string>
    <string name="ui_player_view_stop_previous_media_item">Stop Previous Media Item</string>
    <string name="ui_player_view_skip_multiple_frames_on_same_vsync">Skip Multiple Frames</string>
    <string name="ui_player_view_support_ts_high_profile">Support Media TS High Profile Parsing</string>
    <string name="ui_player_view_support_ts_high_profile_desc">Support playing MPEG-TS files missing AUD or IDR keyframes with Media3 on some devices. Enabling may cause unexpected errors.</string>
    <string name="ui_player_view_extract_header_from_link">Extract Header from Link</string>
    <string name="ui_player_view_extract_header_from_link_desc">Support extracting header info separated by | in the link</string>
    <string name="ui_player_view_display_mode">Global Display Mode</string>
    <string name="ui_player_view_playback_mode">Playback Mode</string>
    <string name="ui_player_view_webview_core">WebView Core</string>
    <string name="ui_player_view_load_timeout">Load Timeout</string>
    <string name="ui_player_view_load_timeout_desc">Affects source switching and reconnection on timeout</string>
    <string name="ui_player_view_buffer_time">Playback Buffer</string>
    <string name="ui_player_view_buffer_time_desc">For Media3, minimum buffer load time before playback (seconds);\n For Ijk, minimum buffer load frames (f) before playback</string>
    <string name="ui_player_view_user_agent">Global UA</string>
    <string name="ui_player_view_rtsp_transport">RTSP Transport</string>
    <string name="ui_player_view_custom_headers">Custom Headers</string>
    <!--
    <string name="ui_player_view_volume_normalization">Volume Normalization</string>
    <string name="ui_player_view_volume_normalization_desc">When enabled, playback volume will be balanced to solve inconsistent volume in subscription sources; only supported by Media3 player</string>
    -->
    <string name="ui_multiview_action_operate_screen">Operate Screen</string>
    <string name="ui_multiview_action_add">Add</string>
    <string name="ui_multiview_action_switch">Switch</string>
    <string name="ui_multiview_action_delete">Delete</string>
    <string name="ui_multiview_action_zoom_in">Zoom In</string>
    <string name="ui_multiview_action_zoom_out">Zoom Out</string>
    <string name="ui_multiview_action_pause">Pause</string>
    <string name="ui_multiview_action_play">Play</string>
    <string name="ui_multiview_action_mute">Mute</string>
    <string name="ui_multiview_action_unmute">Unmute</string>
    <string name="ui_multiview_action_move_screen">Move Screen</string>
    <string name="ui_multiview_action_move_to_screen">Move to Screen</string>
    <string name="ui_close">Close</string>
    <string name="ui_close_not">Do Not Close</string>
    <string name="ui_add_other_playlists">Add Other Playlists</string>
    <string name="ui_channel_view_delete">Delete</string>
    <string name="ui_channel_view_set_current">Set as Current</string>
    <string name="ui_hybrid_mode_disable_auto_add_web_source">Disable Auto Add Web Source</string>
    <string name="ui_hybrid_mode_auto_add_web_source_to_back">Place Auto-added Web Source Lines After Subscription Source</string>
    <string name="ui_hybrid_mode_auto_add_web_source_to_front">Place Auto-added Web Source Lines Before Subscription Source</string>
    <string name="ui_refresh_all">Refresh All</string>
    <string name="ui_add_other_iptv_sources">Add Other Subscription Sources</string>
    <string name="ui_convert_js">Convert JS</string>
    <string name="ui_iptv_source_info">%1$d groups, %2$d channels, %3$d sources</string>
    <string name="ui_time_unit_seconds">Seconds</string>
    <string name="ui_time_unit_minutes">Minutes</string>
    <string name="ui_time_unit_hours">Hours</string>
    <string name="ui_time_unit_days">Days</string>
    <string name="ui_Density_Ratio_adaptive">Adaptive</string>
    <string name="ui_control_up">Up Key/Swipe Up</string>
    <string name="ui_control_down">Down Key/Swipe Down</string>
    <string name="ui_control_left">Left Key/Swipe Left</string>
    <string name="ui_control_right">Right Key/Swipe Right</string>
    <string name="ui_control_select">Select Key</string>
    <string name="ui_control_long_select">Long Press Select Key</string>
    <string name="ui_control_long_up">Long Press Up Key</string>
    <string name="ui_control_long_down">Long Press Down Key</string>
    <string name="ui_control_long_left">Long Press Left Key</string>
    <string name="ui_control_long_right">Long Press Right Key</string>
    <string name="ui_time_show_hidden">Do Not Show Time</string>
    <string name="ui_time_show_always">Always Show Time</string>
    <string name="ui_time_show_every_hour">Show Time %s Seconds Before/After the Hour</string>
    <string name="ui_time_show_half_hour">Show Time %s Seconds Before/After the Half Hour</string>
    <string name="ui_video_player_subtitle_use_system_style">Use System Style</string>
    <string name="ui_video_player_subtitle_use_system_style_desc">Use the font style set in Android system (Settings - Accessibility)</string>
    <string name="ui_video_player_subtitle_follow_embedded_style">Follow Embedded Style</string>
    <string name="ui_video_player_subtitle_follow_embedded_style_desc">Use subtitle style embedded in the video source</string>
    <string name="ui_video_player_subtitle_foreground_color">Font Color</string>
    <string name="ui_video_player_subtitle_background_color">Background Color</string>
    <string name="ui_video_player_subtitle_edge_color">Edge Color</string>
    <string name="ui_video_player_subtitle_window_color">Window Color</string>
    <string name="ui_video_player_subtitle_text_size">Font Size</string>
    <string name="ui_video_player_subtitle_example">Subtitle Example</string>
    <string name="ui_video_player_core_media3_desc">Supports all features except RTSP unicast</string>
    <string name="ui_video_player_core_ijk_desc">Some videos (such as encrypted dash) may not work properly</string>
    <string name="ui_video_player_webview_core_system_desc">System WebView Core</string>
    <string name="ui_video_player_webview_core_x5_desc">Tencent X5 Core, only supports armv7 and arm64 architectures, requires initialization download on first use</string>
    <string name="ui_about_app_id">App ID</string>
    <string name="ui_about_repo">Project Repository</string>
    <string name="ui_about_repo_qrcode_desc">Scan to visit the code repository</string>
    <string name="ui_about_telegram">Discussion Telegram(Chinese language only)</string>
    <string name="ui_about_device_name">Device Name</string>
    <string name="ui_about_device_id">Device ID</string>
    <string name="ui_about_origin_repo">Tianguangyunying Repo</string>
    <string name="ui_about_origin_repo_qrcode_desc">This project owes its existence to Tianguangyunying. Scan to visit the project.</string>
    <string name="ui_about_origin_reward">Reward Tianguangyunying Author</string>
    <string name="ui_about_origin_reward_support">Only offer Weixin(WeChat China Version) reward code</string>
    <string name="ui_about_check_update">Check for Updates</string>
    <string name="ui_about_update_new">New Version: %1$s</string>
    <string name="ui_about_update_none">No Updates</string>
    <string name="ui_update_latest_version">Latest Version: v%1$s</string>
    <string name="ui_update_updating">Updating, please do not close this page</string>
    <string name="ui_update_now">Update Now</string>
    <string name="ui_update_ignore_and_back">Ignore and Return</string>
    <string name="ui_update_is_latest">Already the latest version (tap to return)</string>
    <string name="ui_channel_count">Total %d channels</string>
    <string name="ui_hybrid_mode_disable">Disable</string>
    <string name="ui_hybrid_mode_to_back">Subscription Source Priority</string>
    <string name="ui_hybrid_mode_to_front">Web Source Priority</string>
    <string name="ui_time_shows_hidden">Hidden</string>
    <string name="ui_time_shows_always">Always Show</string>
    <string name="ui_time_shows_every_hour">On the Hour</string>
    <string name="ui_time_shows_half_hour">On the Half Hour</string>
    <string name="ui_keydown_action_previous_channel">Previous Channel</string>
    <string name="ui_keydown_action_next_channel">Next Channel</string>
    <string name="ui_keydown_action_previous_line">Previous Line</string>
    <string name="ui_keydown_action_next_line">Next Line</string>
    <string name="ui_keydown_action_manage_sources">Manage Sources</string>
    <string name="ui_keydown_action_channel_list">Channel List</string>
    <string name="ui_keydown_action_quick_settings">Quick Settings</string>
    <string name="ui_keydown_action_program_list">Program List</string>
    <string name="ui_keydown_action_line_list">Line List</string>
    <string name="ui_keydown_action_playback_control">Playback Control</string>
    <string name="ui_x5_core_preload_success">X5 Core loaded successfully. Restart the app to take effect.</string>
    <string name="ui_x5_core_preload_failure">Failed to load X5 WebView Core</string>
    <string name="ui_x5_core_preload_downloading">Downloading X5 Core remotely. Please do not close the app until the download is complete.</string>
    <string name="ui_x5_core_preload_download_success">X5 Core downloaded successfully!</string>
    <string name="ui_x5_core_preload_download_failure">Failed to get X5 Core. Please use the system WebView core.</string>
    <string name="ui_x5_core_preload_arch_not_supported">X5 architecture not supported</string>
    <string name="ui_channel_info_now_replay">Replaying</string>
    <string name="ui_channel_info_next_play">Playing Next</string>
    <string name="ui_channel_info_now_play">Now Playing</string>
    <string name="ui_hybrid_type_cctv">CCTV</string>
    <string name="ui_hybrid_type_yangshipin">Yangshipin</string>
    <string name="ui_hybrid_type_official_site">Official Site</string>
    <string name="ui_hybrid_type_webview">Other</string>
    <string name="ui_hybrid_type">Web</string>
    <string name="ui_hybrid_type_unknown">Unknown</string>
    <string name="ui_channel_info_reserved">Reserved</string>
    <string name="ui_channel_info_reserve">Reserve</string>
    <string name="ui_channel_info_timeout">Timeout</string>
    <string name="iptv_source_local">Local</string>
    <string name="iptv_source_remote">Remote</string>
    <string name="ui_epg_item_today">Today</string>
    <string name="ui_epg_item_tomorrow">Tom.</string>
    <string name="ui_epg_item_day_after_tomorrow">Overmorrow</string>
    <string name="ui_x5_core_preload_not_supported">X5 Core not available, initializing. Switched to system core.</string>
    <string name="ui_player_view_hls_allow_chunkless_preparation">HLS Allow Chunkless Preparation</string>
    <string name="ui_player_view_hls_allow_chunkless_preparation_desc">Allow Media3 to enable chunkless preparation when playing HLS videos to reduce buffering time. Enabling this option may result in the inability to detect embedded subtitles.</string>
    <string name="ui_video_player_track_closed">Closed</string>
    <string name="ui_settings_language">Language</string>
    <string name="ui_settings_language_desc">Select the application interface language</string>
    <string name="ui_settings_language_selected_tip">Please restart the application to take effect</string>
    <string name="iptv_channel_recent_enable">Enable Recently Watched</string>
    <string name="iptv_channel_recent_enable_desc">Whether to display the list of recently watched channels</string>
    <string name="ui_replace_system_webview">Replace System WebView</string>
    <string name="ui_replace_system_webview_desc">Use the application with package name com.google.android.webview to replace the System WebView kernel (requires restart)</string>
    <string name="ui_player_view_fit_frame_rate">Fit Video Content Frame Rate</string>
    <string name="ui_player_view_fit_frame_rate_desc">After enabling, the player will attempt to adapt to the frame rate of the video content\nRequires enabling system settings and rendering mode to SurfaceView, and system version greater than 11\nDuring the switching period, you may observe screen flickering, and some video sources may not be applicable or may have issues</string>
    <string name="ui_error_clipboard_service_unavailable">Clipboard service unavailable</string>
    <string name="ui_dashboard_module_list_title">Navigation</string>
    <string name="ui_channel_view_webview_player">WebView</string>
    <string name="ui_webview_load_timeout">WebView Load Timeout</string>
    <string name="ui_webview_load_timeout_desc">WebView loading timeout (seconds)</string>
    <string name="ui_video_player_core_vlc_desc">VLC Player, supports more subtitle formats</string>
</resources>