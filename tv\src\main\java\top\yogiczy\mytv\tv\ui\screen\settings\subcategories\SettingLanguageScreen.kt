package top.yogiczy.mytv.tv.ui.screen.settings.subcategories

import android.content.Context
import android.widget.Toast
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.lazy.grid.GridCells
import androidx.compose.foundation.lazy.grid.LazyVerticalGrid
import androidx.compose.foundation.lazy.grid.items
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.Refresh
import androidx.compose.material3.MaterialTheme
import androidx.compose.runtime.Composable
import androidx.compose.runtime.rememberCoroutineScope
import androidx.compose.ui.Modifier
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.text.style.TextOverflow
import androidx.compose.ui.unit.dp
import top.yogiczy.mytv.tv.ui.material.ListItem
import androidx.tv.material3.Text
import kotlinx.coroutines.launch
import top.yogiczy.mytv.tv.R
import top.yogiczy.mytv.tv.ui.rememberChildPadding
import top.yogiczy.mytv.tv.ui.screen.components.AppScaffoldHeaderBtn
import top.yogiczy.mytv.tv.ui.screen.components.AppScreen
import top.yogiczy.mytv.tv.ui.utils.handleKeyEvents
import top.yogiczy.mytv.core.util.utils.setLanguage
import java.util.Locale

@Composable
fun SettingsLanguageScreen(
    modifier: Modifier = Modifier,
    onBackPressed: () -> Unit = {},
) {
    val languageList = listOf(
        "🇨🇳 中文",
        "🇬🇧 English",
        "🕌 عربي",
    )
    val languageMap = mapOf(
        "🇨🇳 中文" to "zh-Hans",
        "🇬🇧 English" to "en",
        "🕌 عربي" to "ar",
    )
    val context = LocalContext.current
    val childPadding = rememberChildPadding()
    AppScreen(
        modifier = modifier,
        header = { Text("${stringResource(R.string.ui_dashboard_module_settings)} / ${stringResource(R.string.ui_channel_view_general)} / ${stringResource(R.string.ui_settings_language)}") },
        canBack = true,
        onBackPressed = onBackPressed,
    ) {
        LazyVerticalGrid(
            modifier = Modifier,
            columns = GridCells.Fixed(6),
            contentPadding = childPadding.copy(top = 10.dp).paddingValues,
            horizontalArrangement = Arrangement.spacedBy(12.dp),
            verticalArrangement = Arrangement.spacedBy(10.dp),
        ) {
            items(languageList, key = { it }) { language ->
                ListItem(
                    modifier = Modifier
                        .fillMaxWidth()
                        .handleKeyEvents(onSelect = {
                            setLanguage(context, languageMap[language])
                            onBackPressed()
                        }),
                    selected = false,
                    headlineContent = {
                        Text(
                            text = language,
                            style = MaterialTheme.typography.bodyLarge,
                            textAlign = TextAlign.Start,
                            overflow = TextOverflow.Ellipsis,
                            maxLines = 2
                        )
                    },
                    onClick = {
                        setLanguage(context, languageMap[language])
                        onBackPressed()
                    },
                )
            }
        }
    }
}